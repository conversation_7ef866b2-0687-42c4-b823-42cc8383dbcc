# coding: utf-8
############################################################################
# Alexandria Cron execution file for automatic download using.
# You can use by manual, but the information will reduse to less.
'''
__author__ = "Nephom, Chiang"
__copyright__ = "Copyright 2016, The Alexandria Project"
__credits__ = ["Nephom, Chiang"]
__license__ = "GPL"
__version__ = "1.0.1"
__maintainer__ = "Nephom, Chiang"
__email__ = "<EMAIL>"
__status__ = "Released"
'''
############################################################################
import sqlite3
import shutil
import re
from pathlib import Path
import yaml
import logging
import os

class BackupManager:
    def __init__(self, config_path):
        # Ensure logging is configured
        FORMAT = '%(asctime)s %(levelname)s: %(message)s'
        logging.basicConfig(level=logging.DEBUG, format=FORMAT, filename='/root/alexandria.log', filemode='a')

        self.config = self._load_config(config_path)

        # Add error handling for config loading
        if not self.config:
            raise ValueError("Configuration could not be loaded")

        # Safely access nested dictionary with .get()
        self.base_path = Path(self.config[0].get('path', {}).get('loa', '.'))
        self.backup_path = Path(self.config[0].get('path', {}).get('old', './backup'))

    def _load_config(self, config_path: str):
        """Load configuration from YAML file with improved error handling"""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
                if not config:
                    logging.error("[config] Empty configuration file")
                    return None
                return config
        except FileNotFoundError:
            logging.error(f"[config] Configuration file not found: {config_path}")
            return None
        except yaml.YAMLError as e:
            logging.error(f"[config] YAML parsing error: {e}")
            return None
        except Exception as e:
            logging.error(f"[config] Failed to load config: {e}")
            return None

    def _process_files(self, files_to_backup):
        """處理需要備份的檔案和資料夾"""
        if not files_to_backup:
            logging.warning("[bmw00] No files/folders specified for backup")
            return

        for relative_path in files_to_backup:
            try:
                # 使用 resolve() 確保完整路徑
                source_path = self.base_path / relative_path
                source_path = source_path.resolve()

                # 檢查路徑是否存在
                if not source_path.exists():
                    logging.warning(f"[bmw02] Path does not exist: {source_path}")
                    continue

                # 確定目標路徑
                target_path = self._determine_target_path(source_path)
                if not target_path:
                    logging.warning(f"[bmw01] No matching backup path found for {source_path}")
                    continue

                # 確保目標目錄存在
                target_path.mkdir(parents=True, exist_ok=True)

                # 處理目錄
                if source_path.is_dir():
                    shutil.move(str(source_path), str(target_path / source_path.name))
                    logging.info(f"[bmi01] Folder moved: {source_path} -> {target_path}/{source_path.name}")
                
                # 處理檔案
                elif source_path.is_file():
                    # 如果是檔案，保留其父目錄結構
                    relative_parent = source_path.relative_to(self.base_path).parent
                    final_target_path = target_path / relative_parent
                    final_target_path.mkdir(parents=True, exist_ok=True)
                    
                    shutil.move(str(source_path), str(final_target_path / source_path.name))
                    logging.info(f"[bmi01] File moved: {source_path} -> {final_target_path}/{source_path.name}")

            except Exception as e:
                logging.error(f"[bme01] Failed to process {relative_path}: {e}", exc_info=True)
                
    def _determine_target_path(self, path):
        """確定備份目標路徑，基於資料夾或檔案名稱"""
        path_str = str(path).lower()
        folder_name = path.parent.name.lower()
        filename = path.name.lower()
        target = None
    
        try:
            # iLO 匹配（不限定大小寫，更寬鬆的匹配）
            if (re.search(r'ilo', path_str, re.IGNORECASE) or 
                re.search(r'ilo', folder_name, re.IGNORECASE) or 
                re.search(r'ilo', filename, re.IGNORECASE)):
                target = self.backup_path / 'FW' / 'iLO'
                # 如果是檔案且父資料夾不是Alexandria，則包含父資料夾
                if path.is_file() and folder_name != 'alexandria':
                    relative_parent = path.parent.relative_to(self.base_path)
                    target = target / relative_parent

            # SPP 匹配
            elif (re.search(r'spp', path_str, re.IGNORECASE) or
                  re.search(r'spp', folder_name, re.IGNORECASE) or
                  re.search(r'spp', filename, re.IGNORECASE)):
                target = self.backup_path / 'SPP'

                # 根據世代分類
                gen_match = (re.search(r'gen(\d+)', path_str, re.IGNORECASE) or
                             re.search(r'gen(\d+)', folder_name, re.IGNORECASE) or
                             re.search(r'gen(\d+)', filename, re.IGNORECASE))
                if gen_match:
                    target = target / f'Gen{gen_match.group(1)}'

                # 提取年份和月份
                date_match = (re.search(r'(\d{4})\.(\d{2})', path_str) or
                              re.search(r'(\d{4})\.(\d{2})', folder_name) or
                              re.search(r'(\d{4})\.(\d{2})', filename))
                if date_match:
                    year = date_match.group(1)
                    month = date_match.group(2)
                    target = target / year / month

                # 提取 BUILD 資訊
                build_match = (re.search(r'(?:BUILD|build|Build)_(\d+)', path_str) or
                               re.search(r'(?:BUILD|build|Build)_(\d+)', folder_name) or
                               re.search(r'(?:BUILD|build|Build)_(\d+)', filename))
                if build_match:
                    target = target / f"BUILD{build_match.group(1)}"

                # 如果是檔案且父資料夾不是Alexandria，則包含父資料夾
                if path.is_file() and folder_name != 'alexandria':
                    relative_parent = path.parent.relative_to(self.base_path)
                    target = target / relative_parent

            # Gaius 匹配
            elif (re.search(r'gaius', path_str, re.IGNORECASE) or 
                  re.search(r'gaius', folder_name, re.IGNORECASE) or 
                  re.search(r'gaius', filename, re.IGNORECASE)):
                target = self.backup_path / 'HPIP'
                # 如果是檔案且父資料夾不是Alexandria，則包含父資料夾
                if path.is_file() and folder_name != 'alexandria':
                    relative_parent = path.parent.relative_to(self.base_path)
                    target = target / relative_parent

            # BIOS 匹配（找尋 A/U + 兩位數字的模式）
            elif (re.search(r'[a-z]\d{2}', path_str, re.IGNORECASE) or 
                  re.search(r'[a-z]\d{2}', folder_name, re.IGNORECASE) or 
                  re.search(r'[a-z]\d{2}', filename, re.IGNORECASE)):
                target = self.backup_path / 'FW' / 'BIOS'
                # 如果是檔案且父資料夾不是Alexandria，則包含父資料夾
                if path.is_file() and folder_name != 'alexandria':
                    relative_parent = path.parent.relative_to(self.base_path)
                    target = target / relative_parent

            # CPLD 匹配
            elif (re.search(r'\.(jed|vme)', path_str) or 
                  re.search(r'\.(CPLD|vme)', folder_name) or 
                  re.search(r'\.(jed|vme)', filename)):
                target = self.backup_path / 'FW' / 'CPLD'
                # 如果是檔案且父資料夾不是Alexandria，則包含父資料夾
                if path.is_file() and folder_name != 'alexandria':
                    relative_parent = path.parent.relative_to(self.base_path)
                    target = target / relative_parent

            # 根據時間戳分類
            #timestamp_match = (re.search(r'@(\d{4})\.(\d{2})', path_str) or 
            #                   re.search(r'@(\d{4})\.(\d{2})', folder_name) or 
            #                   re.search(r'@(\d{4})\.(\d{2})', filename))
            #if timestamp_match:
            #    year, month = timestamp_match.group(1), timestamp_match.group(2)
            #    target = (target or self.backup_path) / year / month

            # 如果以上都不匹配，使用預設路徑
            if target is None:
                target = self.backup_path / 'Unsorted'
                # 如果是檔案且父資料夾不是Alexandria，則包含父資料夾
                if path.is_file() and folder_name != 'alexandria':
                    relative_parent = path.parent.relative_to(self.base_path)
                    target = target / relative_parent

            # 確保目標路徑存在
            target.mkdir(parents=True, exist_ok=True)
            return target

        except Exception as e:
            logging.error(f"[bme02] Error determining target path for {path}: {e}")
            # 返回一個預設的備份路徑
            default_target = self.backup_path / 'Unsorted'
            default_target.mkdir(parents=True, exist_ok=True)
            return default_target

    def run_backup(self, files_to_backup):
        """執行備份過程"""
        if not files_to_backup:
            logging.warning("[bmw02] No files/folders specified for backup")
            return

        self._process_files(files_to_backup)
        logging.info("[bmi03] Backup process completed")
