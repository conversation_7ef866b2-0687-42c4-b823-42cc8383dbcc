'''
__author__ = "<PERSON><PERSON><PERSON>, <PERSON>"
__copyright__ = "Copyright 2016-2020, The Alexandria Project"
__credits__ = ["<PERSON><PERSON><PERSON>, <PERSON>"]
__license__ = "GPL"
__version__ = "1.1"
__maintainer__ = "<PERSON><PERSON><PERSON>, <PERSON>"
__email__ = "<EMAIL>"
__status__ = "Production"
'''

def chrome_option(options):
    options.add_argument('start-maximized')
    options.add_argument('enable-automation')
    options.add_argument('--disable-extensions')
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument("--disable-infobars")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-browser-side-navigation")
    options.add_argument("--disable-notifications")
    options.add_argument('--disable-gpu')
    options.add_argument('--disable-software-rasterizer')
    options.add_experimental_option("prefs", {
        "download.prompt_for_download": Fals<PERSON>,
        "download.directory_upgrade": True,
        "safebrowsing_for_trusted_sources_enabled": False,
        "safebrowsing.enabled": False
    })

    return options

if __name__ == '__main__':
    chrome_option(options)
