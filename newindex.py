import PySimpleGUIWeb as sg
import yaml
import shutil

def main():
    layout = [
            [sg.Text('Change Password')],
            [sg.Text('Default <NAME_EMAIL>')],
            [sg.Combo(['<EMAIL>','<EMAIL>'], key='combo')],
            [sg.Text('Input new password'),sg.InputText('password', key='_password_')],
            [sg.OK(size=(5,1))],
            ]
    
    window = sg.Window('Change Password for LOA', layout, web_port=80,)

    while True:
        e,v = window.read()
        if e is None:
            break
        if e == 'OK':
           #sg.Popup(e, v, v['_password_'])
           with open('/var/www/html/password.yaml', 'r') as fp:
               data = yaml.safe_load(fp)
           for elem in data:
               if elem['name'] == v['combo']:
                   elem['pass'] = v['_password_']
                   break  # no need to iterate further
#           sg.popup(v['combo'], v['_password_'])
           with open('/var/www/html/password.yaml', 'w', encoding='utf-8') as wp:
               print('S')
               yaml.dump(data, wp, default_flow_style=False)
           sg.popup(v['combo'], v['_password_'])

    window.close()

if __name__ == "__main__":
    main()
