# coding: utf-8
############################################################################
# Alexandria Cron execution file for automatic download using.
# You can use by manual, but the information will reduse to less.
'''
__author__ = "Nephom, Chiang"
__copyright__ = "Copyright 2016, The Alexandria Project"
__credits__ = ["Nephom, Chiang"]
__license__ = "GPL"
__version__ = "1.0.4"
__maintainer__ = "Nep<PERSON>, <PERSON>"
__email__ = "<EMAIL>"
__status__ = "Testing"
'''
############################################################################
import logging
import shutil
import requests
import sqlite3
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import List, <PERSON><PERSON>
import yaml
from datetime import datetime
from loa_backup import BackupManager
import time
import sys
import inspect
import threading
from requests.packages.urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class DownloadManager:
    def __init__(self, logger=None, config_path: str = '/root/loa_config.yaml', max_concurrent: int = 5, driver=None, session_refresh_callback=None):
        self.logger = logger or logging.getLogger(__name__)
        self.config = self._load_config(config_path)
        self.base_path = Path(self.config[0]['path']['loa'])
        self.backup_path = Path(self.config[0]['path']['old'])
        self.max_concurrent = max_concurrent
        self.driver = driver
        self.session_refresh_callback = session_refresh_callback
        self.thread_local = threading.local()
        self.db_path = '/root/alexandria.db'
        self._init_database()

    def _load_config(self, config_path: str):
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"[config] Failed to load config: {e}")
            raise

    def _setup_session(self, driver=None) -> requests.Session:
        self.logger.info("[ssi01] Setting up HTTP session")
        session = requests.Session()

        header = requests.utils.default_headers()
        header.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Connection': 'keep-alive',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
        })
        session.headers.update(header)

        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=10,
            max_retries=3,
            pool_block=True
        )
        session.mount('https://', adapter)
        session.mount('http://', adapter)

        self._update_session_cookies(session, driver)
        return session

    def _update_session_cookies(self, session, driver=None):
        try:
            if driver or self.driver:
                driver = driver or self.driver
                cookies_list = driver.get_cookies()
                if not cookies_list:
                    self.logger.warning("[sse01] No cookies found in the browser session!")
                    return False

                cookies_dict = {cookie['name']: cookie['value'] for cookie in cookies_list}
                session.cookies.update(cookies_dict)
                self.logger.info("[ssi02] Cookies updated successfully")
                return True
        except Exception as e:
            self.logger.error(f"[sse02] Cookie update failed: {e}")
            return False

    def _get_session(self):
        if not hasattr(self.thread_local, 'session'):
            self.thread_local.session = self._setup_session(self.driver)
        return self.thread_local.session

    def _close_session(self):
        if hasattr(self.thread_local, 'session'):
            self.thread_local.session.close()
            del self.thread_local.session
            self.logger.info("[ssi03] HTTP session closed for thread")

    def _init_database(self):
        try:
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS download_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        file_path TEXT NOT NULL UNIQUE,
                        webfile BOOLEAN DEFAULT FALSE,
                        skip BOOLEAN DEFAULT FALSE,
                        exist BOOLEAN DEFAULT FALSE,
                        olddata BOOLEAN DEFAULT FALSE,
                        blocked BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                self.logger.info("[dbi01] Database initialized successfully")
        except sqlite3.Error as e:
            self.logger.error(f"[dbe01] SQLite error: {e}")
            raise
        except Exception as e:
            self.logger.error(f"[dbe02] Database initialization failed: {e}")
            raise

    def _update_db_status(self, file_path: str, **kwargs):
        try:
            current_time = datetime.now().isoformat()
            if file_path.startswith(str(self.base_path)):
                clean_file_path = str(Path(file_path).relative_to(self.base_path)).replace('\\', '/')
            else:
                clean_file_path = str(file_path).replace('\\', '/')

            if clean_file_path.endswith('.tmp'):
                return

            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT webfile, skip, exist, olddata, blocked FROM download_status WHERE file_path = ?",
                    (clean_file_path,)
                )
                current_record = cursor.fetchone()

            if current_record:
                current_values = dict(current_record)
            else:
                current_values = {
                    'webfile': False,
                    'skip': False,
                    'exist': False,
                    'olddata': False,
                    'blocked': False
                }

            update_data = {
                'file_path': clean_file_path,
                'webfile': kwargs.get('webfile', current_values['webfile']),
                'skip': kwargs.get('skip', current_values['skip']),
                'exist': kwargs.get('exist', current_values['exist']),
                'olddata': kwargs.get('olddata', current_values['olddata']),
                'blocked': kwargs.get('blocked', current_values['blocked']),
                'updated_at': current_time
            }

            caller_function = inspect.stack()[1].function

            query = """
            INSERT INTO download_status (
                file_path, webfile, skip, exist, olddata, blocked,
                created_at, updated_at
            ) VALUES (
                :file_path, :webfile, :skip, :exist, :olddata, :blocked,
                CURRENT_TIMESTAMP, :updated_at
            ) ON CONFLICT(file_path) DO UPDATE SET
                webfile = :webfile,
                skip = :skip,
                exist = :exist,
                olddata = :olddata,
                blocked = :blocked,
                updated_at = :updated_at;
            """

            self.logger.debug(
                f"[dbe04] Attempting to update DB from {caller_function}: "
                f"File: {clean_file_path}, Data: {update_data}"
            )

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute(query, update_data)
                conn.commit()
                self.logger.debug(f"[dbe05] DB update committed: File: {clean_file_path}")

                cursor.execute(
                    "SELECT * FROM download_status WHERE file_path = :file_path",
                    {'file_path': clean_file_path}
                )
                updated_record = cursor.fetchone()
                if updated_record:
                    self.logger.debug(f"[dbe06] DB record updated successfully: {dict(updated_record)}")
                else:
                    self.logger.error(f"[dbe07] DB record not found after update for: {clean_file_path}")
        except sqlite3.Error as e:
            self.logger.error(f"[dbe07] SQLite error while updating status for {file_path}: {e}")
            raise
        except Exception as e:
            self.logger.error(f"[dbe08] Error updating database for {file_path}: {e}")
            raise

    def _get_file_status(self, file_path: str) -> Tuple[bool, bool, bool, bool, bool]:
        try:
            if file_path.startswith(str(self.base_path)):
                clean_file_path = str(Path(file_path).relative_to(self.base_path)).replace('\\', '/')
            else:
                clean_file_path = str(file_path).replace('\\', '/')

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT skip, webfile, exist, olddata, blocked FROM download_status WHERE file_path = ?",
                    (clean_file_path,)
                )
                return cursor.fetchone() or (False, False, False, False, False)
        except Exception as e:
            self.logger.error(f"[dbe04] Failed to get status for {file_path}: {e}")
            return False, False, False, False, False

    def _cleanup_local_files(self, download_paths: List[str]):
        try:
            for tmp_file in self.base_path.rglob('*.tmp'):
                try:
                    tmp_file.unlink()
                    self.logger.info(f"[cle00] Removed incomplete temporary file: {tmp_file}")
                except Exception as e:
                    self.logger.error(f"[cle01] Failed to remove temporary file {tmp_file}: {e}")

            all_existing_files = {
                str(path.relative_to(self.base_path)).replace('\\', '/')
                for path in self.base_path.rglob('*')
                if path.is_file() and not path.suffix.lower() == '.tmp'
            }

            for relative_path in all_existing_files:
                try:
                    self._update_db_status(str(relative_path), skip=True, webfile=False, exist=True)
                except Exception as e:
                    self.logger.error(f"[cle02] Failed to update database for {relative_path}: {e}")

            normalized_download_paths = {
                str(Path(path)).replace('\\', '/')
                for path in download_paths
            }

            manager = BackupManager('/root/loa_config.yaml')

            for path in self.base_path.rglob('*'):
                if not path.exists():
                    continue

                try:
                    relative_path = str(path.relative_to(self.base_path)).replace('\\', '/')
                    if path.is_file():
                        if relative_path not in normalized_download_paths:
                            self.logger.info(f"[cle07] File not in download list: {relative_path}")
                            self._update_db_status(str(relative_path), olddata=True)
                            manager.run_backup([relative_path])
                            continue

                        if self._is_empty_or_html(path):
                            self.logger.info(f"[cle08] Empty or HTML file detected: {relative_path}")
                            path.unlink()
                            self._update_db_status(str(relative_path), skip=False, exist=False)
                            continue

                        self._update_db_status(str(relative_path), skip=True, webfile=False, exist=True)
                        self.logger.debug(f"[cle09] Valid file updated: {relative_path}")
                    elif path.is_dir():
                        if not any(path.iterdir()):
                            relative_path = str(path.relative_to(self.base_path)).replace('\\', '/')
                            try:
                                with sqlite3.connect(self.db_path) as conn:
                                    cursor = conn.cursor()
                                    cursor.execute("DELETE FROM download_status WHERE file_path = ?", (relative_path,))
                                    conn.commit()
                            except Exception as e:
                                self.logger.error(f"[cle05] Failed to remove database record for empty dir {relative_path}: {e}")
                            shutil.rmtree(path)
                            self.logger.info(f"[cle02] Removed empty directory: {path}")
                except Exception as e:
                    self.logger.error(f"[cle03] Error processing path {path}: {e}")
                    continue

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT file_path FROM download_status WHERE blocked = 0")
                db_file_paths = {row[0] for row in cursor.fetchall()}
                missing_in_local = db_file_paths - all_existing_files
                if missing_in_local:
                    for path in missing_in_local:
                        try:
                            self.logger.warning(f"[pdi04] File in database but not in filesystem: {path}")
                            cursor.execute("DELETE FROM download_status WHERE file_path = ?", (path,))
                            conn.commit()
                            self.logger.info(f"[cle10] Removed database entry for non-existent local file: {path}")
                        except Exception as e:
                            self.logger.error(f"[cle14] Failed to remove database entry for {path}: {e}")
                else:
                    self.logger.info("[cle15] All blocked=0 database entries are valid.")
            self.logger.info("[cle04] Cleanup process completed successfully")
        except Exception as e:
            self.logger.error(f"[cle01] Cleanup failed: {e}")
            raise

    def _is_empty_or_html(self, file_path: Path) -> bool:
        """
        檢查文件是否為空或是意外變成HTML格式的文件
    
        Returns:
            True: 文件為空或是意外變成HTML格式（需要踢除）
            False: 文件正常（保留）
        """
        if not file_path.exists() or file_path.stat().st_size == 0:
            return True
    
        try:
            # 檢查文件擴展名，如果是標準 HTML 文件則不跳過
            html_extensions = {'.html', '.htm', '.xhtml', '.shtml'}
            if file_path.suffix.lower() in html_extensions:
                # 這些文件本來就應該是HTML格式，所以保留
                self._update_db_status(str(file_path), webfile=False)
                return False
        
            # 只對非 HTML 擴展名的文件進行內容檢查
            html_signatures = [
                b'<!doc',           # <!DOCTYPE 的開頭
                b'<html',           # <html> 標籤
                b'</html>',         # </html> 結尾標籤
                b'</body>',         # </body> 結尾標籤
                b'<head>',          # <head> 標籤
                b'<title>',         # <title> 標籤
            ]
        
            # 檢查文件開頭
            with open(file_path, 'rb') as f:
                header = f.read(1024).lower()
                if any(signature in header for signature in html_signatures):
                    # 非HTML擴展名的文件卻包含HTML內容，標記為webfile並踢除
                    self._update_db_status(str(file_path), webfile=True)
                    return True
        
            # 檢查文件結尾（針對可能很大的HTML文件）
            try:
                with open(file_path, 'rb') as f:
                    f.seek(-min(1024, file_path.stat().st_size), 2)  # 避免小文件seek錯誤
                    footer = f.read().lower()
                    if any(signature in footer for signature in html_signatures):
                        # 非HTML擴展名的文件卻包含HTML內容，標記為webfile並踢除
                        self._update_db_status(str(file_path), webfile=True)
                        return True
            except (OSError, IOError):
                # 如果無法seek到文件末尾，忽略這個檢查
                pass
        
            # 文件看起來正常，保留
            self._update_db_status(str(file_path), webfile=False)
            return False
        
        except Exception as e:
            self.logger.error(f"[che01] Error checking file {file_path}: {e}")
            # 發生錯誤時保守處理，踢除文件
            return True

    def _download_file(self, url: str, target_path: Path) -> bool:
        max_retries = 3
        retry_count = 0
        temp_path = None
        while retry_count < max_retries:
            try:
                target_path.parent.mkdir(parents=True, exist_ok=True)
                session = self._get_session()
                with session.get(url, stream=True, timeout=(30, 600), allow_redirects=True) as response:
                    response.raise_for_status()
                    temp_path = target_path.with_name(target_path.name + '.tmp')
                    with open(temp_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                if self._is_empty_or_html(temp_path):
                    self.logger.debug("File appears to be HTML!")
                    temp_path.unlink(missing_ok=True)
                    return False
                temp_path.rename(target_path)
                self._update_db_status(str(target_path), skip=True, exist=True, webfile=False, blocked=False)
                return True
            except Exception as e:
                self.logger.error(f"[dfe02] Download attempt {retry_count + 1} failed for {url}: {e}")
                retry_count += 1
                if temp_path and temp_path.exists():
                    temp_path.unlink(missing_ok=True)
                self._close_session()
                time.sleep(retry_count * 2)
        skip, webfile, exist, olddata, blocked = self._get_file_status(str(target_path))
        self.logger.error(f"[dfe01] All download attempts failed for {url}")
        if not blocked:
            self._update_db_status(str(target_path), olddata=False, blocked=True)
        else:
            self._update_db_status(str(target_path), olddata=True, blocked=True)
        return False

    def process_download_queue(self, download_list: List[Tuple[str, str]], max_workers: int = None) -> List[str]:
        download_paths = [path.replace('%5C%5C', '/') for _, path in download_list]
        self._cleanup_local_files(download_paths)
        download_queue = []
        for url, path in download_list:
            relative_path = path.replace('%5C%5C', '/')
            target_path = self.base_path / relative_path
            db_record = self._get_file_status(str(relative_path))
            if db_record:
                skip, webfile, exist, olddata, blocked = db_record
            else:
                skip, webfile, exist, olddata, blocked = False, False, False, False, False
            if blocked and olddata:
                self.logger.info(f"Cannot download from LOA: {relative_path}, SKIP to download")
                continue
            if not exist or not skip or webfile:
                download_queue.append((url, relative_path))
        successful_downloads = []
        max_workers = min(max_workers or self.max_concurrent, len(download_queue), 5)
        if download_queue:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_url = {
                    executor.submit(self._download_file, url, self.base_path / relative_path): relative_path
                    for url, relative_path in download_queue
                }
                for future in as_completed(future_to_url):
                    relative_path = future_to_url[future]
                    target_path = self.base_path / relative_path
                    if future.result() and not self._is_empty_or_html(target_path):
                        successful_downloads.append(relative_path)
                    else:
                        self.logger.warning(f"[pdi02] Downloaded file is HTML or failed: {relative_path}")
        self.logger.info(f"[pdi01] Download complete. Successful: {len(successful_downloads)}/{len(download_queue)}")
        return successful_downloads
