##############################################################
# Alexandria manual download version, don't use on crontab
# Creater: <PERSON><PERSON><PERSON>, <PERSON>.
'''
__author__ = "<PERSON><PERSON><PERSON>, <PERSON>"
__copyright__ = "Copyright 2016-2020, The Alexandria Project"
__credits__ = ["Nephom, Chiang"]
__license__ = "GPL"
__version__ = "2.1-0010"
__maintainer__ = "N<PERSON><PERSON>, <PERSON>"
__email__ = "<EMAIL>"
__status__ = "Production"
'''
##############################################################

from selenium import webdriver
from cryptography.fernet import Fernet
from pathlib import Path
from bs4 import BeautifulSoup
import shutil
import sys
import importlib
import time
import os
import re
import requests
import urllib3
import logging
import hashlib
import yaml
importlib.reload(sys)
sys.setrecursionlimit(1000000)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

FORMAT = '%(asctime)s %(levelname)s: %(message)s'
logging.basicConfig(level=logging.INFO, format=FORMAT, filename='/root/alexandria.log', filemode='w')
'''Using YAML to save account and password'''
if os.path.isfile('/var/www/html/pass.yaml'):
    with open('/var/www/html/pass.yaml', 'r') as accpass:
        ap = yaml.safe_load(accpass)
else:
    with open('/var/www/html/password.yaml', 'r') as accpass:
        ap = yaml.safe_load(accpass)

def end_browser(driver):
    driver.close()
    driver.quit()
    sys.exit(0)

def mail2():
    logging.info('Mail2 function')
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    from email.header import Header

    sender = '<EMAIL>'
    receivers = [ap[0]['name']]
    cc = [ap[0]['name'], '<EMAIL>']
    message = MIMEMultipart('alternative')
    message['From'] = sender
    message['To'] =  ','.join(receivers)
    message['CC'] =  ','.join(cc)

    subject = 'Alexandria account password expired!'
    message['Subject'] = Header(subject, 'utf-8')

    html = """\
<!DOCTYPE html>
Hello Niko,<br>
    <p></p>
The account password of Alexandria has been expired. Please update it on http://*************/niko.<br>
    <p></p>
<p>Thanks,</p>
<p>ISVT AI Robot</p>
"""
    message.attach(MIMEText(html, 'html', 'utf-8'))
    try:
        print('Send mail')
        smtpObj = smtplib.SMTP('***********')
        smtpObj.sendmail(sender, receivers, message.as_string())
    except smtplib.SMTPException as e:
        logging.error('Error on mail2 function.', exc_info=True)

def main():
    path = '/mnt/Alexandria'
    options = webdriver.ChromeOptions()
    options.add_argument('--disable-extensions')
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument("--disable-notifications")
    options.add_argument('--verbose')
    options.add_argument('--disable-software-rasterizer')
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-infobars")
    options.add_experimental_option("prefs", {
        "download.default_directory": path,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing_for_trusted_sources_enabled": False,
        "safebrowsing.enabled": False
})

    capabilities = webdriver.DesiredCapabilities.CHROME
    chrome_driver_binary = "/usr/bin/chromedriver"

    try:
        logging.info('Create brower drive for using')
        driver = webdriver.Chrome(chrome_driver_binary, options=options, desired_capabilities=capabilities)
        driver.set_page_load_timeout(600)

        logging.info('Get Alexandria Login Passport web page')
        driver.get('https://alexandria.itcs.hpe.com/Account/LoginPassport')

        logging.info('Login!')
        while True:
            userinput = driver.find_elements_by_tag_name('input')
            for name in userinput:
                if 'UserName' in name.get_attribute('name'):
                    name.send_keys(ap[0]['name'])
            passinput = driver.find_elements_by_tag_name('input')
            for word in passinput:
                if 'Password' in word.get_attribute('name'):
                    word.send_keys(ap[0]['pass'])
            for submit in driver.find_elements_by_tag_name('button'):
                if 'submit' in submit.get_attribute('type'):
                    submit.click()
            if 'Download Files - The Library of Alexandria' not in driver.title:
                logging.info('Please retry with Kali account')
                userinput = driver.find_elements_by_tag_name('input')
                for name in userinput:
                    if 'UserName' in name.get_attribute('name'):
                        name.send_keys(ap[1]['name'])
                passinput = driver.find_elements_by_tag_name('input')
                for word in passinput:
                    if 'Password' in word.get_attribute('name'):
                        word.send_keys(ap[1]['pass'])
                for submit in driver.find_elements_by_tag_name('button'):
                    if 'submit' in submit.get_attribute('type'):
                        submit.click()
                if 'Download Files - The Library of Alexandria' not in driver.title:
                    logging.info('Please contact with Niko or Kali, EXIT!')
                    mail2()
                    end_browser(driver)
            else:
                break

        logging.info('Wait for loading...')
        time.sleep(30)
        driver.find_element_by_xpath("//*[contains(@id, 'inventec')]/i").click()
        logging.info('Start to check webpage on Alexandria.')
        folderlist = []
        for folder in driver.find_elements_by_class_name('jstree-anchor'):
            if 'Inventec' in folder.text:
                pass
            else:
                folderlist.append(folder.text)

        '''Remove Empty Folder First'''
        fold = list(os.walk(path))[1:]
        if 'crdownload' in fold:
            pass
        else:
            for fol in fold:
                ''' folder example: ('FOLDER/3', [SUB-Folder], ['file'])'''
                if not fol[2]:
                    logging.info('Delete Empty Folder '+fol[0])
                    os.rmdir(fol[0])

        for item1 in sorted(os.listdir(path)):
            for item2 in sorted(folderlist):
                if re.sub(" +", "", item1) == re.sub(" +", "", item2):
                    folderlist.remove(item2)
                else:
                    pass

        onefile = []
        upca = BeautifulSoup(driver.page_source, "html.parser")
        table = upca.find_all('ul', class_='jstree-children')
        for uptag in table:
            litag = uptag.find_all_next('li', class_="jstree-node jstree-leaf")
            for dlitag in litag:
                onefile.append(dlitag.get_text())
            break
        try:
            for uptag in table:
                '''Check last file'''
                litag = uptag.find_all_next('li', class_="jstree-node jstree-leaf jstree-last")
                for dlitag in litag:
                    logging.info('dlitag'+dlitag.get_text())
                    onefile.append(dlitag.get_text())
                break
        except:
            pass

        for item3 in sorted(os.listdir(path)):
            for item4 in sorted(onefile):
                if re.sub(" +","", item3.lower()) == re.sub(" +","",item4.lower()):
                    onefile.remove(item4)
                else:
                    pass

        if len(folderlist) >= 1:
            pass
        else:
            logging.info('No update! exit!')
            end_browser(driver)
        '''Search how many folder items under Inventec tree.'''
        total_count_list=[]
        for c in range(1,999):
            if (driver.find_elements_by_xpath('//*/div[4]/ul/li/ul/li['+str(c)+']')):
                total_count_list.append(driver.find_elements_by_xpath('//*/div[4]/ul/li/ul/li['+str(c)+']'))
        url_list = []
        url_path = 'https://alexandria.itcs.hpe.com/CheckDownload/GetFile?somepath=%5C%5CInventec%5C%5C'
        '''download_path = 'https://alexandria.itcs.hpe.com/Download/DownloadFiles'''
        logging.info('Start to analyzing what files need to download.')
        for olink in onefile:
            ofilename = re.sub(' ','%20', olink)
            tspace = re.sub(' {2,}','%20%20', ofilename)
            url_list.append('{}Signal{}'.format(url_path,tspace))
        os.chdir(path)
        for i in folderlist:
            for j in range(1, len(total_count_list) + 1):
                for item in driver.find_elements_by_xpath('//*/div[4]/ul/li/ul/li['+str(j)+']'):
                    if re.sub(" +", "", i.lower()) in item.get_attribute('id'):
                        if 'jstree-leaf' in item.get_attribute('class'):
                            filename = driver.find_element_by_xpath('//*/div[4]/ul/li/ul/li['+str(j)+']/a').text
                            if re.sub(" +", "", i.lower()) in re.sub(" +", "", filename.lower()):
                                logging.info('Single File {} start to download.'.format(filename))
                                url_list.append('{}Signal{}'.format(url_path, re.sub(" +", "%20", filename)))
                        elif 'jstree-close' in item.get_attribute('class'):
                            driver.find_element_by_xpath('//*/div[4]/ul/li/ul/li['+str(j)+']/i').click()
                        item_count_list=[]
                        '''Search how many folder items on foldercom tree.'''
                        for c in range(1,100):
                            if (driver.find_elements_by_xpath('//*/div[4]/ul/li/ul/li['+str(j)+']/ul/li['+str(c)+']')):
                                item_count_list.append(driver.find_elements_by_xpath('//*/div[4]/ul/li/ul/li['+str(j)+']/ul/li['+str(c)+']'))
                                if len(item_count_list) > 1:
                                    for k in range(1, len(item_count_list) + 1):
                                        filename = driver.find_element_by_xpath('//*/div[4]/ul/li/ul/li['+str(j)+']/ul/li['+str(k)+']/a').text
                                        url_list.append('{}{}%5C%5C{}'.format(url_path,i,filename))
                                elif len(item_count_list) == 1:
                                    filename = driver.find_element_by_xpath('//*/div[4]/ul/li/ul/li['+str(j)+']/ul/li[1]/a').text
                                    url_list.append('{}{}%5C%5C{}'.format(url_path,i,filename))

        mail_list = []
        s = requests.session()
        header = requests.utils.default_headers()
        header['User-Agent'] = 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36'
        adapter = requests.adapters.HTTPAdapter(pool_connections=100, pool_maxsize=100)
        s.mount('https://', adapter)
        s.headers.update({'Connection':'Keep-Alive'})
        cookies_list = driver.get_cookies()
        if cookies_list:
            logging.info('Cookies: {}'.format(cookies_list))
        else:
            logging.error('ERROR, No cookies!')
            end_browser(driver)
        cookies_dict = []
        for cookie in cookies_list:
            cookies_dict.append([cookie['name'],cookie['value']])
        cookies_dict = dict(cookies_dict)
        
        from tqdm import tqdm
        import threading
        def down(url, foldername, filesname, cookie):
            logging.info('Foldername: {}'.format(foldername))
            logging.info('Filename: {}'.format(filesname))
            logging.info('Download {}'.format(url))
           
            try:
                with s.get(url, stream=True, verify=False, headers=header, cookies=cookies_dict) as r:
                    if r.status_code == 200:
                        total_size_in_bytes= int(r.headers.get('content-length', 0))
                        block_size = 1024 #1 Kibibyte
                        progress_bar = tqdm(total=total_size_in_bytes, unit='iB', unit_scale=True)
                        with open(foldername+'/'+filesname, 'wb') as f:
                            for data in r.iter_content(block_size):
                                progress_bar.update(len(data))
                                f.write(data)
                        progress_bar.close()
                        if total_size_in_bytes != 0 and progress_bar.n != total_size_in_bytes:
                            logging.error("ERROR, something went wrong")
                    else:
                        logging.error("Status code not 200. Reason: {}", r.reason)
            except:
                logging.error('Error on download {}'.format(filesname))
                logging.info('Try to switch to selenium to get again.')
                driver.get(url)

        def download_process(urls):
            logging.info('All URL list: {}'.format(urls))
            for url in urls:
                logging.info('Now process {}'.format(url))
                if 'Signal' in url:
                    logging.info('File loop')
                    filepname = re.sub('http*.*Signal','', url)
                    filesname = re.sub('%20+',' ', filepname)
                    surl = re.sub('Signal','', url)
                    logging.info(filesname)
                    thread = threading.Thread(target=down, args=(surl, '', filesname, cookies_dict))
                    thread.setDaemon(True)
                    thread.start()
                    urls.remove(url)
                    continue
                else:
                    logging.info('Folder loop')
                    prefoldername = re.sub('http*.*tec%5C%5C','', url)
                    foldername = re.sub('%5C%5C.*','', prefoldername)
                    Path(path+'/'+foldername).mkdir(parents=True, exist_ok=True)
                    Path(path+'/'+foldername).chmod(0o777)
                    filesname = re.sub('http*.*%5C%5C*.*%5C%5C','', url)
                    logging.info(filesname)
                    thread = threading.Thread(target=down, args=(url, foldername, filesname, cookies_dict))
                    thread.setDaemon(True)
                    thread.start()
                    urls.remove(url)
                    continue
            main_thread = threading.current_thread()
            for t in threading.enumerate():
                if t is main_thread:
                    continue
                t.join()
            return urls
        
        urls = sorted(set(url_list))
        res = ''
        try:
            res = download_process(urls=urls)
        except Exception as e:
            logging.error("When process {} an Error occurred.".format(res))
            logging.error("Error!", exc_info=True)
            end_browser(driver)

        logging.info('All download thread were done.')
        print('All download thread were done.')
        '''Close Chromium driver'''
        end_browser(driver)
    except Exception as e:
        logging.error("Error!", exc_info=True)
        end_browser(driver)

if __name__ == '__main__':
    main()

