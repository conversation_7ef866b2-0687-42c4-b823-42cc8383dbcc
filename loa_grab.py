# coding: utf-8
############################################################################
# Alexandria Cron execution file for automatic download using.
# You can use by manual, but the information will reduse to less.
'''
__author__ = "Nephom, Chiang"
__copyright__ = "Copyright 2016, The Alexandria Project"
__credits__ = ["Nephom, Chiang"]
__license__ = "GPL"
__version__ = "1.0.0"
__maintainer__ = "Nep<PERSON>, Chiang"
__email__ = "<EMAIL>"
__status__ = "Released"
'''
############################################################################
import re
import logging
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

def get_file_structure_with_urls(driver, base_url, timeout=20):
    file_structure = []
    wait = WebDriverWait(driver, timeout)
    
    def encode_name(name):
        """編碼名稱中的空格"""
        encoded = re.sub(' ', '%20', name.strip())
        return re.sub(' {2,}', '%20%20', encoded)

    def decode_path(path):
        """解碼路徑中的%20"""
        return path.replace('%20', ' ')
    
    def process_items(parent_element, current_path=""):
        """遞迴處理所有層級的項目"""
        try:
            # 先點擊展開當前元素（如果是可展開的）
            if 'jstree-closed' in parent_element.get_attribute('class'):
                try:
                    icon = parent_element.find_element(By.CLASS_NAME, 'jstree-icon')
                    driver.execute_script("arguments[0].click();", icon)
                    # 等待展開完成
                    wait.until(lambda d: 'jstree-open' in parent_element.get_attribute('class'))
                except Exception as e:
                    logging.error(f"[gfe01]: {e}")

            # 獲取當前項目的名稱
            anchor = parent_element.find_element(By.CLASS_NAME, 'jstree-anchor')
            item_name = anchor.get_attribute('textContent').strip()
            
            if 'Inventec' in item_name:
                return
                
            encoded_name = encode_name(item_name)
            full_path = f"{current_path}%5C%5C{encoded_name}" if current_path else encoded_name
            #print(f"{full_path}")
            
            # 檢查是否為檔案
            if 'jstree-leaf' in parent_element.get_attribute('class'):
                url_path = f"{base_url}{full_path}"
                file_structure.append((url_path, decode_path(full_path)))
                #print(f"添加檔案: {url_path}")
                return

            # 尋找並處理所有子項目
            try:
                # 使用 JavaScript 等待子項目載入
                driver.execute_script("""
                    return new Promise(resolve => {
                        setTimeout(resolve, 700);  // 等待子項目載入
                    });
                """)
                
                sub_items = parent_element.find_elements(
                    By.XPATH, 
                    ".//li[contains(@class, 'jstree-node')]"
                )
                
                if sub_items:
                    #print(f"找到 {len(sub_items)} 個子項目在 {item_name}")
                    for sub_item in sub_items:
                        process_items(sub_item, full_path)
                else:
                    # 如果沒有子項目但是資料夾，也要記錄
                    url_path = f"{base_url}{full_path}"
                    file_structure.append((url_path, decode_path(full_path)))
                    #print(f"添加資料夾: {url_path}")
                    
            except Exception as e:
                logging.error(f"[gfe02]: {e}")
                # 如果出錯，至少記錄當前資料夾
                url_path = f"{base_url}{full_path}"
                file_structure.append((url_path, decode_path(full_path)))
                
        except Exception as e:
            logging.error(f"[gfe03]: {e}")
    
    try:
        # 獲取所有頂層項目
        root_items = driver.find_elements(By.XPATH, "//li[contains(@class, 'jstree-node')]")
        total_items = len(root_items)
        logging.info(f"Find {len(root_items)} Top item")
        
        # 處理每個頂層項目
        for current_index, item in enumerate(root_items, 1):
            logging.info(f"{current_index}/{total_items} items")
            anchor = item.find_element(By.CLASS_NAME, 'jstree-anchor')
            current_name = anchor.get_attribute('textContent').strip()
            logging.info(f"Current item: {current_name}")
            process_items(item)
            
    except Exception as e:
        logging.error(f"[gfe04]: {e}")
    
    return file_structure
