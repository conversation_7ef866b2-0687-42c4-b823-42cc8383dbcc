# coding: utf-8
'''
__author__ = "<PERSON><PERSON><PERSON>, <PERSON>"
__copyright__ = "Copyright 2016-2020, The Alexandria Project"
__credits__ = ["<PERSON><PERSON><PERSON>, <PERSON>"]
__license__ = "GPL"
__version__ = "1.0.1"
__maintainer__ = "<PERSON><PERSON><PERSON>, <PERSON>"
__email__ = "<EMAIL>"
__status__ = "Production"
'''

def mail():
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    from email.header import Header

    sender = '<EMAIL>'
    receivers = ['<EMAIL>']

    message = MIMEMultipart('alternative')
    message['From'] = sender
    message['To'] =  ','.join(receivers)

    subject = 'Alexandria Error notice'
    message['Subject'] = Header(subject, 'utf-8')

    html = """\
<!DOCTYPE html>
Hi Admin,<br>
    <p></p>
The Alexandria had error occurred. Please refer attached file.<br>
    <p></p>
<p>Thanks,</p>
<p>ISVT AI Robot</p>
"""

    att2 = MIMEText(open('/root/alexandria.log', 'rb').read(), 'base64', 'utf-8')
    att2["Content-Type"] = 'application/octet-stream'
    att2["Content-Disposition"] = 'attachment; filename="alexandria.log"'
    
    message.attach(MIMEText(html, 'html', 'utf-8'))
    message.attach(att2)
    smtpObj = smtplib.SMTP('***********')
    smtpObj.sendmail(sender, receivers, message.as_string())

def main():
    mail()

if __name__ == '__main__':
    main()

