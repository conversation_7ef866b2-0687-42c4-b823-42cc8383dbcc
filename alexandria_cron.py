# coding: utf-8
############################################################################
# Alexandria Cron execution file for automatic download using.
# You can use by manual, but the information will reduse to less.
'''
__author__ = "Nephom, Chiang"
__copyright__ = "Copyright 2016, The Alexandria Project"
__credits__ = ["Nephom, Chiang"]
__license__ = "GPL"
__version__ = "3.2.2.2"
__maintainer__ = "Nep<PERSON>, Chiang"
__email__ = "<EMAIL>"
__status__ = "Pre-Released"
'''
############################################################################

import sys
from chrome_option import chrome_option
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.support import expected_conditions as EC
from loa_mail_error import error
'''notify need pass a variable'''
from loa_mail_notify import notify
from loa_mail_aerror import aerror
from bs4 import BeautifulSoup
from pathlib import Path
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import shutil
import importlib
import time
import os
import re
import requests
import urllib3
import logging
import yaml
import glob
importlib.reload(sys)
sys.setrecursionlimit(1000000)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

FORMAT = '%(asctime)s %(levelname)s: %(message)s'
logging.basicConfig(level=logging.INFO, format=FORMAT, filename='/root/alexandria.log', filemode='a')
from loa_down import DownloadManager
from loa_grab import get_file_structure_with_urls

'''YAML main account and password'''
if os.path.isfile('/var/www/html/password.yaml'):
    with open('/var/www/html/password.yaml', 'r') as accpass:
        data = yaml.safe_load(accpass)
        ap = [{'name': item['name'], 'pass': item['pass']} for item in data if 'name' in item and 'pass' in item]
else:
    print('No LOA password file in /var/www/html/!')
    logging.error('[enve02] No password file in /var/www/html/')
    error()
    sys.exit(1)

'''System Configuration'''
if os.path.isfile('/root/loa_config.yaml'):
    with open('/root/loa_config.yaml', 'r') as loa_config:
        loaconfig = yaml.safe_load(loa_config)
else:
    print('No LOA Configuration in /root/!')
    logging.error('[enve01] No configuration file in /root')
    error()
    sys.exit(1)

def check_url_reachable(url: str) -> bool:
    """
    Check if the given URL is reachable
    
    Args:
        url (str): URL to check
        
    Returns:
        bool: True if reachable, False otherwise
    """
    try:
        response = requests.get(url, verify=False, timeout=10)
        if response.status_code == 200:
            logging.info(f"[cir01] URL {url} is reachable.")
            return True
        else:
            logging.error(f"[cire01] URL {url} returned status code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logging.error(f"[cire02] Failed to connect to {url}: {str(e)}")
        return False

def end_browser(driver):
    if driver:
        try:
            driver.quit()
            sys.exit(0)
        except Exception as e:
            logging.error(f"[ebe] Error quitting driver: {e}")

def get_error_flag_file(account_name):
    """取得錯誤標記檔案的路徑"""
    # 使用帳號名稱創建唯一的檔案名
    safe_name = account_name.replace('@', '_').replace('.', '_')
    return f"error_flag_{safe_name}.tmp"

def has_sent_error_email(account_name):
    """檢查是否已經發送過錯誤郵件（檢查檔案是否存在）"""
    flag_file = get_error_flag_file(account_name)
    return os.path.exists(flag_file)

def mark_error_sent(account_name):
    """標記已發送錯誤郵件（創建標記檔案）"""
    flag_file = get_error_flag_file(account_name)
    try:
        with open(flag_file, 'w', encoding='utf-8') as f:
            f.write(f"Error email sent for account: {account_name}\n")
            f.write(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logging.info(f"[mi05] 已創建錯誤標記檔案: {flag_file}")
    except Exception as e:
        logging.warning(f"[mw02] 無法創建標記檔案: {e}")

def clear_error_flag(account_name):
    """清除錯誤標記（刪除標記檔案）"""
    flag_file = get_error_flag_file(account_name)
    try:
        if os.path.exists(flag_file):
            os.remove(flag_file)
            logging.info(f"[mi06] 已刪除錯誤標記檔案: {flag_file}")
    except Exception as e:
        logging.warning(f"[mw03] 無法刪除標記檔案: {e}")

def login_process(ap: list[dict], options=None, login_url: str = None) -> webdriver.Chrome:
    """
    執行登入流程並返回已登入的 WebDriver
    
    Args:
        ap (list[dict]): 包含帳號密碼的列表，格式: [{'name': 'user', 'pass': 'pwd'}]
        options: Chrome選項
        login_url: 登入頁面URL
    
    Returns:
        webdriver.Chrome: 已登入的WebDriver實例
        
    Raises:
        LoginError: 當所有帳號都登入失敗時拋出
    """
    class LoginError(Exception):
        pass
    
    def create_driver():
        """創建並返回新的WebDriver實例"""
        return webdriver.Chrome(options=options)
    
    def check_login(driver) -> bool:
        """檢查是否已登入"""
        return 'Download Files - The Library of Alexandria' in driver.title
    
    def try_login(driver, account: dict) -> bool:
        """
        嘗試使用給定帳號登入
        
        Returns:
            bool: 登入是否成功
        """
        try:
            # 等待帳號輸入框出現，最多等待10秒
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, 'email-sign-in'))
            ).send_keys(account['name'])
            
            driver.find_element(By.CLASS_NAME, 'submit-btn').click()
            
            # 等待密碼輸入框出現，最多等待10秒
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, 'password-sign-in'))
            ).send_keys(account['pass'])
            
            driver.find_element(By.CLASS_NAME, 'submit-btn').click()
            
            # 等待登入後的頁面加載完成，檢查標題
            WebDriverWait(driver, 10).until(
                lambda d: 'Download Files - The Library of Alexandria' in d.title
            )
            
            logging.info(f"[mi02] 帳號 {account['name']} 登入成功！")
            clear_error_flag(account['name'])
            return True
            
        except Exception as e:
            logging.warning(f"[mw01] Account {account['name']} login failed: {e}")
            if not has_sent_error_email(account['name']) and 'kali' in account['name']:
                aerror(account['name'])  # Send an error email
                mark_error_sent(account['name'])
                logging.info(f"[mi03] 錯誤郵件已發送給帳號: {account['name']}")
            else:
                logging.info(f"[mi04] 帳號 {account['name']} 已發送過錯誤郵件，跳過發送")

            return False

    # 主要登入流程
    try:
        driver = create_driver()
        logging.info(f"[li01] Check {login_url}")
        driver.get(login_url)
        wait = WebDriverWait(driver, 30)  # 最长等待30秒
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        
        if check_login(driver):
            logging.info("[li02] 已經處於登入狀態")
            return driver
            
        # 嘗試所有帳號
        for account in ap:
            if try_login(driver, account):
                return driver
                
            logging.info("[li02] Close WebDriver and re-open a new one!")
            
            # 關閉當前 WebDriver 並重啟
            try:
                driver.quit()
            except Exception as quit_error:
                logging.error(f"[le01] 關閉 WebDriver 時發生錯誤: {quit_error}")
                
            logging.warning("[lw01] Re-open new WebDriver")
            driver = create_driver()
            driver.get(login_url)
            
        # 如果所有帳號都失敗
        logging.error("[lw01] LOA Login failure with all password accounts!")
        raise LoginError("All login attempts failed")
        
    except Exception as e:
        if 'driver' in locals():
            try:
                driver.quit()
            except:
                pass
        raise LoginError(f"Login process failed: {str(e)}")

def refresh_session_and_driver(download_manager):
    """
    主程式中的 session 刷新回調函數
    負責重新登入和更新 driver
    """
    try:
        # 重新登入、獲取新的 driver
        new_driver = login_process(
                ap=ap,  # 您的帳號列表
                options=options,  # Chrome選項
                login_url=login_url  # 登入URL
                )

        # 更新 download_manager 的 driver
        download_manager.driver = new_driver

        # 重置 session
        download_manager.session = None

        logging.info("[session] Successfully refreshed driver and session")
    except Exception as e:
        logging.error(f"[session] Refresh failed: {e}")
        raise

def main():
    path = loaconfig[0]['path']['loa']
    path2 = loaconfig[0]['path']['old']
    options = Options()
    options = chrome_option(options)
    chrome_driver_binary = "/usr/bin/chromedriver"
    flag_file = "/root/aerror.flag"
    driver = None
    login_url = 'https://alexandria-itcs.it.hpe.com/Account/LoginOkta'
    base_url = 'https://alexandria-itcs.it.hpe.com/CheckDownload/GetFile?somepath=%5C%5CInventec%5C%5C'

    try:
        # Check if login URL is reachable
        if not check_url_reachable(login_url):
            logging.error('[me01] Login URL is not reachable, terminating program.')
            error()
            sys.exit(1)

        logging.info('[mi01] Start to Login!')
        driver=login_process(
            ap=ap,  # 您的帳號列表
            options=options,  # Chrome選項
            login_url=login_url  # 登入URL
        )

        try:
            waitlogin = WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.XPATH, "//*[contains(@id, 'inventec')]/i")))
        except TimeoutException:
            logging.error('[me02] Timeout to load LOA')
            end_browser(driver)
            sys.exit(0)

        waitlogin.click()
        logging.info('[mi01] Loading done.')
        if os.path.exists(flag_file):
            os.remove(flag_file)
        logging.info('[mi01] Start to check webpage on Alexandria.')
        url_list = []
        logging.info('[mi01] Start to analyzing what files need to download in sub-folder.')

        url_list = get_file_structure_with_urls(driver, base_url)
        mail_list = []
        logging.info(f"[mi01] Ready to download, the total url count: {len(url_list)}")
        if len(url_list) == 0:
            logging.error('[mue] No Update!!!!! WHY?!')
            error()
            end_browser(driver)
        else:
            try:
                # 創建 DownloadManager 時傳入 session 刷新回調
                download_manager = DownloadManager(
                    driver=driver,
                    session_refresh_callback=refresh_session_and_driver
                )

                try:
                    # 下載流程
                    mail_list = download_manager.process_download_queue(url_list)

                    if isinstance(mail_list, list):
                        logging.info(f"[mi03] Successfully downloaded: {len(mail_list)} files")
                    else:
                        logging.warning(f"[mw02] Something Wrong: {mail_list}")

                except Exception as download_error:
                    # 第一次下載失敗，嘗試刷新 session
                    logging.warning(f"[dw01] Download failed, attempting to refresh session: {download_error}")
                    try:
                        # 手動調用 session 刷新回調
                        refresh_session_and_driver(download_manager)

                        # 重新嘗試下載
                        mail_list = download_manager.process_download_queue(url_list)
                        if isinstance(mail_list, list):
                            logging.info(f"[mi04] Successfully downloaded after session refresh: {len(mail_list)} files")
                        else:
                            logging.warning(f"[mw03] Still something wrong after session refresh: {mail_list}")

                    except Exception as refresh_error:
                        logging.error(f"[dme02] Failed to recover from download error: {refresh_error}")
                        raise

            except Exception as e:
                logging.error(f"[dme01] Program error: {str(e)}")

            if len(mail_list) == 0:
                logging.info('[mi04] No list to mail')
            else:
                logging.info('[mi04] Start to send mail.')
                report = notify(mail_list)
                if report == 1:
                    logging.error('[mlne] Error on loa_mail_notify.py')
                else:
                    logging.info('[mi04] Mail Sent done')

            logging.info('[mdi] Close all connection and program')
    except Exception as e:
        logging.error("[mte] Error! {}".format(e))
        #error()
    finally:
        end_browser(driver)

if __name__ == '__main__':
    main()
