# coding: utf-8
############################################################################
# Alexandria Cron execution file for automatic download using.
# You can use by manual, but the information will reduse to less.
'''
__author__ = "Nephom, Chiang"
__copyright__ = "Copyright 2016, The Alexandria Project"
__credits__ = ["Nephom, Chiang"]
__license__ = "GPL"
__version__ = "1.0.5"
__maintainer__ = "Nep<PERSON>, Chiang"
__email__ = "<EMAIL>"
__status__ = "Production"
'''
############################################################################

def aerror(ap):
    from loa_mail_aerror import aerror
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    from email.header import Header
    import smtplib

    sender = '<EMAIL>'
    if ap == '0':
        receiver = ['<EMAIL>', '<EMAIL>']
        ap = 'Niko and Kali'
    else:
        receiver = [ap]
    cc = ['<EMAIL>']
    receivers = receiver + cc
    message = MIMEMultipart('alternative')
    message['From'] = sender
    message['To'] =  ','.join(receiver)
    message['Cc'] =  ','.join(cc)

    subject = 'Alexandria account password expired!'
    message['Subject'] = Header(subject, 'utf-8')

    html = """\
<!DOCTYPE html>
Hello """+ap+""",<br>
    <p></p>
The account password of Alexandria has been expired. Please update it on http://**********/update/.<br>
    <p></p>
<p>Thanks,</p>
<p>ISVT AI Robot</p>
"""

    message.attach(MIMEText(html, 'html', 'utf-8'))
    try:
        smtpObj = smtplib.SMTP('***********')
        smtpObj.sendmail(sender, receivers, message.as_string())
        return 0
    except smtplib.SMTPException as e:
        return 1

def main():
    aerror(ap)

if __name__ == '__main__':
    main()
