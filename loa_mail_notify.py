# coding: utf-8
############################################################################
# Alexandria Cron execution file for automatic download using.
# You can use by manual, but the information will reduse to less.
'''
__author__ = "Nephom, Chiang"
__copyright__ = "Copyright 2016, The Alexandria Project"
__credits__ = ["Nephom, Chiang"]
__license__ = "GPL"
__version__ = "1.0.5"
__maintainer__ = "Nephom, Chiang"
__email__ = "<EMAIL>"
__status__ = "Pre-Release"
'''
############################################################################

def notify(mail_list):
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    from email.header import Header
    
    sender = '<EMAIL>'
    receivers = ['<EMAIL>','<EMAIL>','<EMAIL>']
    
    message = MIMEMultipart('alternative')
    message['From'] = sender
    message['To'] =  ','.join(receivers)
    subject = 'Alexandria update notice'
    message['Subject'] = Header(subject, 'utf-8')
    
    html = """\
<!DOCTYPE html>
<html>
<head></head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div class="email-container" style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <div class="header" style="background-color: #4a90e2; color: white; padding: 10px; text-align: center;">
            <h1>Alexandria Update Notice</h1>
        </div>
        <div class="content" style="background-color: #f4f4f4; padding: 20px; text-align: center;">
            <p style="font-size: 18px;">An update for Alexandria software/firmware has been successfully downloaded.</p>
            <table class="files-table" style="width: 100%; border-collapse: separate; border-spacing: 0; margin-top: 15px; box-shadow: 0 2px 3px rgba(0,0,0,0.1);">
                <thead>
                    <tr>
                        <th style="background-color: #4a90e2; color: white; padding: 10px; text-align: left; border-top-left-radius: 5px; border-top-right-radius: 5px;">
                            Downloaded Files
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {}
                </tbody>
            </table>

            <p>Thanks,</p>
            <p>SIT Lab Robot</p>
        </div>
        <div class="footer" style="background-color: #f4f4f4; color: #777; text-align: center; padding: 10px; font-size: 0.8em;">
            ©2016 SIT Lab Robot | Automated Notification
        </div>
    </div>
</body>
</html>
"""
    mailset = set(mail_list)
    rows = ""
    for article in mailset:
        file_path = f"\\\\192.168.1.2\\osvt_storage\\download\\Alexandria\\{str(article)}"
        display_name = str(article)  # 顯示 folder/file 或 file
        rows += f'<tr><td><a href="file://{file_path}" style="color: #4a90e2; text-decoration: none;">{display_name}</a></td></tr>'
    
    html = html.format(rows)
    message.attach(MIMEText(html, 'html', 'utf-8'))
    
    try:
        smtpObj = smtplib.SMTP('192.168.1.9')
        smtpObj.sendmail(sender, receivers, message.as_string())
        return 0
    except smtplib.SMTPException as e:
        return 1

def main():
    notify(mail_list)

if __name__ == '__main__':
    main()
